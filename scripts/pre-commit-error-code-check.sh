#!/bin/bash

# Git pre-commit hook for error code validation
# 将此文件复制到 .git/hooks/pre-commit 并设置执行权限

echo "🔍 检查错误码..."

# 检查是否有 define/err_code.go 文件被修改
changed_files=$(git diff --cached --name-only | grep "define/err_code.go")

if [ -z "$changed_files" ]; then
    echo "✅ 没有错误码文件被修改，跳过检查"
    exit 0
fi

echo "📝 发现错误码文件被修改:"
echo "$changed_files"

# 运行错误码检查
if ! go run scripts/check_error_codes.go .; then
    echo ""
    echo "❌ 错误码检查失败！"
    echo "请修复上述问题后再提交。"
    echo ""
    echo "💡 提示:"
    echo "1. 检查错误码是否重复"
    echo "2. 确认错误码在模块分配范围内"
    echo "3. 参考 docs/error_code_registry.md 了解分配规则"
    exit 1
fi

echo "✅ 错误码检查通过"
exit 0
