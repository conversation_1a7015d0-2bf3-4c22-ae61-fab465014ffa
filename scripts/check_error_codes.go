package main

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"path/filepath"
	"strconv"
	"strings"
)

// ErrorCode 错误码信息
type ErrorCode struct {
	Code   int
	Name   string
	File   string
	Line   int
	Module string
}

// ModuleRange 模块错误码范围
type ModuleRange struct {
	Module string
	Start  int
	End    int
}

var moduleRanges = []ModuleRange{
	{"synthesis", 100001, 199999},
	{"story", 110001, 119999},
	{"announcement", 200001, 209999},
	{"market_changes", 210001, 219999},
	{"operation_announcement", 220001, 229999},
	{"bonus_mall", 300001, 309999},
	{"notification", 310001, 319999},
	{"yc", 400001, 409999},
	{"card_community", 500001, 599999},
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run check_error_codes.go <project_root>")
		os.Exit(1)
	}

	projectRoot := os.Args[1]
	errorCodes, err := scanErrorCodes(projectRoot)
	if err != nil {
		fmt.Printf("Error scanning error codes: %v\n", err)
		os.Exit(1)
	}

	// 检查重复
	duplicates := findDuplicates(errorCodes)
	if len(duplicates) > 0 {
		fmt.Println("❌ 发现重复错误码:")
		for code, items := range duplicates {
			fmt.Printf("错误码 %d 重复使用:\n", code)
			for _, item := range items {
				fmt.Printf("  - %s:%d (%s) in %s\n", item.File, item.Line, item.Name, item.Module)
			}
		}
	}

	// 检查范围违规
	violations := checkRangeViolations(errorCodes)
	if len(violations) > 0 {
		fmt.Println("❌ 发现错误码范围违规:")
		for _, v := range violations {
			fmt.Printf("  - %s:%d 错误码 %d (%s) 不在模块 %s 的分配范围内\n", 
				v.File, v.Line, v.Code, v.Name, v.Module)
		}
	}

	if len(duplicates) == 0 && len(violations) == 0 {
		fmt.Println("✅ 错误码检查通过，无重复和范围违规")
	}

	// 输出统计信息
	fmt.Printf("\n📊 错误码统计:\n")
	moduleStats := make(map[string]int)
	for _, ec := range errorCodes {
		moduleStats[ec.Module]++
	}
	
	for module, count := range moduleStats {
		fmt.Printf("  - %s: %d 个错误码\n", module, count)
	}
}

func scanErrorCodes(projectRoot string) ([]ErrorCode, error) {
	var errorCodes []ErrorCode
	
	err := filepath.Walk(projectRoot, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		// 只扫描 define/err_code.go 文件
		if !strings.HasSuffix(path, "define/err_code.go") {
			return nil
		}
		
		// 提取模块名
		module := extractModuleName(path, projectRoot)
		
		codes, err := parseErrorCodes(path, module)
		if err != nil {
			return fmt.Errorf("parsing %s: %v", path, err)
		}
		
		errorCodes = append(errorCodes, codes...)
		return nil
	})
	
	return errorCodes, err
}

func extractModuleName(filePath, projectRoot string) string {
	rel, _ := filepath.Rel(projectRoot, filePath)
	parts := strings.Split(rel, string(filepath.Separator))
	
	// apps/business/card_community/define/err_code.go -> card_community
	if len(parts) >= 3 && parts[0] == "apps" {
		return parts[2]
	}
	
	// apps/platform/common/define/err_code.go -> common
	if len(parts) >= 3 && parts[1] == "platform" {
		return parts[2]
	}
	
	return "unknown"
}

func parseErrorCodes(filePath, module string) ([]ErrorCode, error) {
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, filePath, nil, parser.ParseComments)
	if err != nil {
		return nil, err
	}
	
	var errorCodes []ErrorCode
	
	ast.Inspect(node, func(n ast.Node) bool {
		if genDecl, ok := n.(*ast.GenDecl); ok && genDecl.Tok == token.VAR {
			for _, spec := range genDecl.Specs {
				if valueSpec, ok := spec.(*ast.ValueSpec); ok {
					for i, name := range valueSpec.Names {
						if i < len(valueSpec.Values) {
							if code := extractErrorCode(valueSpec.Values[i]); code > 0 {
								pos := fset.Position(name.Pos())
								errorCodes = append(errorCodes, ErrorCode{
									Code:   code,
									Name:   name.Name,
									File:   filePath,
									Line:   pos.Line,
									Module: module,
								})
							}
						}
					}
				}
			}
		}
		return true
	})
	
	return errorCodes, nil
}

func extractErrorCode(expr ast.Expr) int {
	if callExpr, ok := expr.(*ast.CallExpr); ok {
		if len(callExpr.Args) >= 1 {
			if basicLit, ok := callExpr.Args[0].(*ast.BasicLit); ok {
				if basicLit.Kind == token.INT {
					if code, err := strconv.Atoi(basicLit.Value); err == nil {
						return code
					}
				}
			}
		}
	}
	return 0
}

func findDuplicates(errorCodes []ErrorCode) map[int][]ErrorCode {
	codeMap := make(map[int][]ErrorCode)
	
	for _, ec := range errorCodes {
		codeMap[ec.Code] = append(codeMap[ec.Code], ec)
	}
	
	duplicates := make(map[int][]ErrorCode)
	for code, items := range codeMap {
		if len(items) > 1 {
			duplicates[code] = items
		}
	}
	
	return duplicates
}

func checkRangeViolations(errorCodes []ErrorCode) []ErrorCode {
	var violations []ErrorCode
	
	for _, ec := range errorCodes {
		// 跳过通用错误码（8位数）
		if ec.Code >= 10000000 {
			continue
		}
		
		inRange := false
		for _, mr := range moduleRanges {
			if ec.Module == mr.Module && ec.Code >= mr.Start && ec.Code <= mr.End {
				inRange = true
				break
			}
		}
		
		if !inRange {
			violations = append(violations, ec)
		}
	}
	
	return violations
}
