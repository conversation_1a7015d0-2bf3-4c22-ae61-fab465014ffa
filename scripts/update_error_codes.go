package main

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
)

// 错误码映射表
var errorCodeMapping = map[string]string{
	// 帖子相关错误
	"define.CC40001Err": "define.CC500501Err", // 用户未登录 -> 通用验证错误
	"define.CC40011Err": "define.CC500001Err", // 帖子不存在
	"define.CC40012Err": "define.CC500002Err", // 帖子状态无效
	"define.CC40013Err": "define.CC500003Err", // 无权限操作帖子
	"define.CC40014Err": "define.CC500004Err", // 帖子描述不能为空
	"define.CC40015Err": "define.CC500005Err", // 收购价格必须大于0
	"define.CC40016Err": "define.CC500006Err", // 收购价格最多只能输入一位小数
	"define.CC40017Err": "define.CC500007Err", // 媒体文件格式错误
	"define.CC40018Err": "define.CC500008Err", // 违规下架的帖子不能编辑
	"define.CC40019Err": "define.CC500009Err", // 已删除的帖子不能编辑
	"define.CC40020Err": "define.CC500010Err", // 违规下架的帖子只能删除

	// 会话相关错误
	"define.CC40101Err": "define.CC500101Err", // 会话不存在
	"define.CC40102Err": "define.CC500102Err", // 会话ID不能为空
	"define.CC40103Err": "define.CC500103Err", // 对方ID不能为空
	"define.CC40104Err": "define.CC500104Err", // 对方用户不存在
	"define.CC40105Err": "define.CC500105Err", // 获取用户信息失败
	"define.CC40004Err": "define.CC500106Err", // 不能与自己创建会话

	// 消息相关错误
	"define.CC40201Err": "define.CC500201Err", // 消息内容为空
	"define.CC40202Err": "define.CC500202Err", // 消息内容过长
	"define.CC40203Err": "define.CC500203Err", // 图片上传失败
	"define.CC40204Err": "define.CC500204Err", // 消息重复
	"define.CC40205Err": "define.CC500205Err", // 接收者ID不能为空
	"define.CC40206Err": "define.CC500206Err", // 消息类型无效

	// 智能回复相关错误
	"define.CC40301Err": "define.CC500301Err", // 模板不存在
	"define.CC40302Err": "define.CC500302Err", // 模板内容为空

	// 商家申请相关错误
	"define.CC40401Err": "define.CC500401Err", // 您已有待审核的申请
	"define.CC40402Err": "define.CC500402Err", // 申请记录不存在
	"define.CC40403Err": "define.CC500403Err", // 申请已被审核
	"define.CC40404Err": "define.CC500404Err", // 您已是商家

	// 通用验证错误
	"define.CC40002Err": "define.CC500502Err", // 参数不能为空
	"define.CC40003Err": "define.CC500503Err", // 参数格式错误
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run update_error_codes.go <project_root>")
		os.Exit(1)
	}

	projectRoot := os.Args[1]
	
	// 只更新 card_community 模块的文件
	cardCommunityPath := filepath.Join(projectRoot, "apps", "business", "card_community")
	
	err := filepath.Walk(cardCommunityPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		// 只处理 .go 文件，跳过 err_code.go（已经手动更新）
		if !strings.HasSuffix(path, ".go") || strings.HasSuffix(path, "err_code.go") {
			return nil
		}
		
		return updateFile(path)
	})
	
	if err != nil {
		fmt.Printf("Error updating files: %v\n", err)
		os.Exit(1)
	}
	
	fmt.Println("✅ 错误码更新完成")
}

func updateFile(filePath string) error {
	content, err := ioutil.ReadFile(filePath)
	if err != nil {
		return err
	}
	
	originalContent := string(content)
	updatedContent := originalContent
	
	// 应用所有映射
	hasChanges := false
	for oldCode, newCode := range errorCodeMapping {
		if strings.Contains(updatedContent, oldCode) {
			updatedContent = strings.ReplaceAll(updatedContent, oldCode, newCode)
			hasChanges = true
		}
	}
	
	// 如果有变更，写回文件
	if hasChanges {
		err = ioutil.WriteFile(filePath, []byte(updatedContent), 0644)
		if err != nil {
			return err
		}
		fmt.Printf("✅ 更新文件: %s\n", filePath)
	}
	
	return nil
}
