#!/bin/bash

# 安装 Git hooks 脚本

echo "🔧 安装 Git hooks..."

# 检查是否在 Git 仓库中
if [ ! -d ".git" ]; then
    echo "❌ 错误：当前目录不是 Git 仓库"
    exit 1
fi

# 创建 hooks 目录（如果不存在）
mkdir -p .git/hooks

# 安装 pre-commit hook
if [ -f ".git/hooks/pre-commit" ]; then
    echo "⚠️  pre-commit hook 已存在，创建备份..."
    cp .git/hooks/pre-commit .git/hooks/pre-commit.backup.$(date +%Y%m%d_%H%M%S)
fi

# 复制错误码检查脚本到 pre-commit hook
cp scripts/pre-commit-error-code-check.sh .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit

echo "✅ Git hooks 安装完成"
echo ""
echo "📋 已安装的 hooks:"
echo "  - pre-commit: 错误码重复和范围检查"
echo ""
echo "💡 使用方法:"
echo "  - 正常提交代码，hook 会自动运行"
echo "  - 手动检查: make check-error-codes"
echo "  - 跳过检查: git commit --no-verify"
