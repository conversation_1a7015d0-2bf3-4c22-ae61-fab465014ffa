package service

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/repo"
	commondefine "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/facade"
	"app_service/pkg/search"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// GetMerchantApplicationListForAdmin 获取管理端商家申请列表
func (s *Service) GetMerchantApplicationListForAdmin(req *define.GetMerchantApplicationAdminListReq) (*define.GetMerchantApplicationAdminListResp, error) {
	merchantAppSchema := repo.GetQuery().MerchantApplication
	queryBuilder := search.NewQueryBuilder()

	// 根据申请时间筛选
	if !req.StartTime.IsZero() && !req.EndTime.IsZero() {
		queryBuilder = queryBuilder.Gte(merchantAppSchema.AppliedAt, req.StartTime)
		queryBuilder = queryBuilder.Lte(merchantAppSchema.AppliedAt, req.EndTime)
	}

	// 根据用户ID筛选
	if req.UserID != "" {
		queryBuilder = queryBuilder.Eq(merchantAppSchema.UserID, req.UserID)
	}

	// 根据申请状态筛选
	if req.Status != 0 && req.Status.IsValid() {
		queryBuilder = queryBuilder.Eq(merchantAppSchema.Status, int32(req.Status))
	}

	// 按申请时间倒序排列
	queryBuilder = queryBuilder.OrderByDesc(merchantAppSchema.AppliedAt)

	queryWrapper := queryBuilder.Build()

	// 分页查询
	applications, total, err := repo.NewMerchantApplicationRepo(merchantAppSchema.WithContext(s.ctx)).SelectPage(
		queryWrapper,
		req.GetPage(),
		req.GetPageSize(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询商家申请列表失败: %v", err)
		return nil, commondefine.CommonErr.Err(err)
	}

	// 如果没有申请记录，直接返回空列表
	if len(applications) == 0 {
		return &define.GetMerchantApplicationAdminListResp{
			List:  []*define.GetMerchantApplicationAdminListData{},
			Total: 0,
		}, nil
	}

	// 收集所有用户ID
	userIDs := make([]string, 0, len(applications))
	for _, app := range applications {
		userIDs = append(userIDs, app.UserID)
	}

	// 批量获取用户信息
	userMap, err := facade.GetNewUserMap(s.ctx, userIDs)
	if err != nil {
		log.Ctx(s.ctx).Errorf("批量获取用户信息失败: %v", err)
		return nil, define.CC500105Err
	}

	// 构建响应数据
	list := make([]*define.GetMerchantApplicationAdminListData, 0, len(applications))
	for _, app := range applications {
		data := &define.GetMerchantApplicationAdminListData{
			ID:         app.ID,
			UserID:     app.UserID,
			Status:     app.GetStatus(),
			AppliedAt:  app.AppliedAt,
			ReviewedAt: app.ReviewedAt,
		}

		// 设置用户名称
		if user, exists := userMap[app.UserID]; exists && user != nil {
			data.UserName = user.Nickname
		} else {
			data.UserName = "未知用户"
		}

		list = append(list, data)
	}

	return &define.GetMerchantApplicationAdminListResp{
		List:  list,
		Total: total,
	}, nil
}

// ReviewMerchantApplication 审核商家申请
func (s *Service) ReviewMerchantApplication(req *define.ReviewMerchantApplicationReq) (*define.ReviewMerchantApplicationResp, error) {
	// 获取当前管理员ID（审核人ID）
	reviewerID := s.userService.GetAdminId()

	// 查询申请记录
	merchantAppSchema := repo.GetQuery().MerchantApplication
	queryWrapper := search.NewQueryBuilder().
		Eq(merchantAppSchema.ID, req.ID).
		Build()

	application, err := repo.NewMerchantApplicationRepo(merchantAppSchema.WithContext(s.ctx)).SelectOne(queryWrapper)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Ctx(s.ctx).Warnf("商家申请记录不存在，申请ID：%s", req.ID)
			return nil, define.CC500402Err.SetMsg("申请记录不存在")
		}
		log.Ctx(s.ctx).Errorf("查询商家申请记录失败，申请ID：%s，错误：%v", req.ID, err)
		return nil, commondefine.CommonErr.Err(err)
	}

	// 根据审核结果更新申请状态
	if *req.Approved {
		application.Approve(reviewerID)
		log.Ctx(s.ctx).Infof("商家申请审核通过，申请ID：%s，用户ID：%s，审核人：%s", req.ID, application.UserID, reviewerID)
	} else {
		application.Reject(reviewerID)
		log.Ctx(s.ctx).Infof("商家申请审核拒绝，申请ID：%s，用户ID：%s，审核人：%s", req.ID, application.UserID, reviewerID)
	}

	// 更新申请记录
	err = repo.NewMerchantApplicationRepo(merchantAppSchema.WithContext(s.ctx)).UpdateById(application)
	if err != nil {
		log.Ctx(s.ctx).Errorf("更新商家申请记录失败，申请ID：%s，错误：%v", req.ID, err)
		return nil, commondefine.CommonErr.Err(err)
	}

	// 记录操作成功日志
	log.Ctx(s.ctx).Infof("商家申请审核完成，申请ID：%s，审核结果：%v，审核人：%s",
		req.ID, req.Approved, reviewerID)

	return &define.ReviewMerchantApplicationResp{
		ID: req.ID,
	}, nil
}
