package service

import (
	"strconv"
	"time"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	"app_service/apps/business/card_community/service/logic"
	commondefine "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/facade"
	"app_service/pkg/search"
	"app_service/pkg/util/snowflakeutl"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// CreateConversation 创建会话
func (s *Service) CreateConversation(req *define.CreateConversationReq) (*define.CreateConversationResp, error) {
	userID := s.userService.GetUserId()

	// 参数验证
	if req.OtherParticipantID == "" {
		return nil, define.CC500103Err
	}
	if userID == req.OtherParticipantID {
		return nil, define.CC500106Err
	}

	// 检查是否已存在会话
	conversationSchema := repo.GetQuery().Conversation
	queryWrapper := search.NewQueryBuilder().
		Eq(conversationSchema.ParticipantID, userID).
		Eq(conversationSchema.OtherParticipantID, req.OtherParticipantID).
		Eq(conversationSchema.IsDeleted, int32(0)).
		Build()

	existingConversation, err := repo.NewConversationRepo(conversationSchema.WithContext(s.ctx)).SelectOne(queryWrapper)
	if err != nil && err != gorm.ErrRecordNotFound {
		log.Ctx(s.ctx).Errorf("查询已存在会话失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 如果会话已存在，直接返回
	if existingConversation != nil {
		return &define.CreateConversationResp{
			ID:    existingConversation.ID,
			IsNew: false,
		}, nil
	}

	// 验证对方用户是否存在
	_, err = facade.GetNewUser(s.ctx, req.OtherParticipantID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取对方用户信息失败: %v", err)
		return nil, define.CC500104Err
	}

	// 生成会话ID
	conversationID := strconv.FormatInt(snowflakeutl.GenerateID(), 10)
	otherConversationID := strconv.FormatInt(snowflakeutl.GenerateID(), 10)

	// 创建双向会话记录
	now := time.Now()

	// 当前用户的会话记录（发起创建会话的是用户）
	userConversation := &model.Conversation{
		ID:                 conversationID,
		ParticipantID:      userID,
		ParticipantType:    enums.ParticipantTypeUser.Int32(), // 1=用户
		OtherParticipantID: req.OtherParticipantID,
		UnreadCount:        0,
		IsDeleted:          0,
		Status:             enums.StatusLimited.Int32(), // 用户默认限制中
		CreatedAt:          now,
		UpdatedAt:          now,
	}

	// 对方的会话记录（对方是商家）
	otherConversation := &model.Conversation{
		ID:                 otherConversationID,
		ParticipantID:      req.OtherParticipantID,
		ParticipantType:    enums.ParticipantTypeMerchant.Int32(), // 2=商家
		Status:             enums.StatusNormal.Int32(),            // 商家默认正常
		OtherParticipantID: userID,
		UnreadCount:        0,
		IsDeleted:          0,
		CreatedAt:          now,
		UpdatedAt:          now,
	}

	// 使用事务创建双向记录
	err = repo.GetDB().WithContext(s.ctx).Transaction(func(tx *gorm.DB) error {
		conversationRepo := repo.NewConversationRepo(conversationSchema.WithContext(s.ctx))

		// 创建当前用户的会话记录
		if err := conversationRepo.Save(userConversation); err != nil {
			log.Ctx(s.ctx).Errorf("创建用户会话记录失败: %v", err)
			return commondefine.CommonErr
		}

		// 创建对方的会话记录
		if err := conversationRepo.Save(otherConversation); err != nil {
			log.Ctx(s.ctx).Errorf("创建对方会话记录失败: %v", err)
			return commondefine.CommonErr
		}

		return nil
	})

	if err != nil {
		log.Ctx(s.ctx).Errorf("创建会话失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 会话创建成功后，同步检查是否需要发送智能回复（确保用户进入会话时能看到智能回复）
	shouldSend, template, err := logic.ShouldSendSmartReplyOnConversationCreate(s.ctx, userID, req.OtherParticipantID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("检查智能回复条件失败: %v", err)
		// 智能回复检查失败不影响会话创建结果
	} else if shouldSend && template != nil {
		// 发送智能回复（独立事务，失败不影响会话创建结果）
		err = logic.SendSmartReplyOnConversationCreate(s.ctx, conversationID, userID, req.OtherParticipantID, template)
		if err != nil {
			log.Ctx(s.ctx).Errorf("发送智能回复失败: %v", err)
			// 智能回复发送失败不影响会话创建结果
		}
	}

	return &define.CreateConversationResp{
		ID:    conversationID,
		IsNew: true,
	}, nil
}

// GetConversationList 获取会话列表
func (s *Service) GetConversationList(req *define.GetConversationListReq) (*define.GetConversationListResp, error) {
	userID := s.userService.GetUserId()

	// 查询当前用户的会话列表
	conversationSchema := repo.GetQuery().Conversation
	queryBuilder := search.NewQueryBuilder().
		Eq(conversationSchema.ParticipantID, userID).
		Eq(conversationSchema.IsDeleted, int32(0)).      // 只查询未删除的会话
		OrderByDesc(conversationSchema.LastMessageTime). // 按最后消息时间倒序
		OrderByDesc(conversationSchema.CreatedAt)        // 如果没有消息，按创建时间倒序

	// 查询会话列表
	queryWrapper := queryBuilder.Build()

	// 添加分页条件
	queryWrapper.ScopeOpts = append(queryWrapper.ScopeOpts, search.MakePaginate(req.GetPageSize(), req.GetPage()))

	conversations, err := repo.NewConversationRepo(conversationSchema.WithContext(s.ctx)).SelectList(queryWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询会话列表失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 如果没有会话，直接返回空列表
	if len(conversations) == 0 {
		return &define.GetConversationListResp{
			List:             []*define.GetConversationListData{},
			HasMore:          false,
			TotalUnreadCount: 0,
		}, nil
	}

	// 收集所有对方用户ID
	otherUserIDs := make([]string, 0, len(conversations))
	for _, conv := range conversations {
		otherUserIDs = append(otherUserIDs, conv.OtherParticipantID)
	}

	// 批量获取对方用户信息
	otherUserMap, err := facade.GetNewUserMap(s.ctx, otherUserIDs)
	if err != nil {
		log.Ctx(s.ctx).Errorf("批量获取用户信息失败: %v", err)
		return nil, define.CC500105Err
	}

	// 使用聚合查询计算总未读消息数
	var totalUnreadCount int64
	err = repo.GetDB().WithContext(s.ctx).Model(&model.Conversation{}).
		Where("participant_id = ? AND is_deleted = 0", userID).
		Select("COALESCE(SUM(unread_count), 0)").
		Scan(&totalUnreadCount).Error
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询总未读消息数失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 转换为响应格式
	list := make([]*define.GetConversationListData, 0, len(conversations))

	for _, conv := range conversations {
		otherUser := otherUserMap[conv.OtherParticipantID]
		if otherUser == nil {
			log.Ctx(s.ctx).Warnf("用户信息不存在: %s", conv.OtherParticipantID)
			continue
		}

		// 构造对方用户信息
		otherParticipant := define.UserInfo{
			ID:     conv.OtherParticipantID,
			Name:   otherUser.Nickname,
			Avatar: otherUser.Avatar,
		}

		list = append(list, &define.GetConversationListData{
			ID:                 conv.ID,
			OtherParticipant:   otherParticipant,
			LastMessageContent: conv.LastMessageContent,
			LastMessageTime:    conv.LastMessageTime,
			LastMessageType:    enums.MessageType(conv.LastMessageType),
			UnreadCount:        int(conv.UnreadCount),
			CreatedAt:          conv.CreatedAt,
		})
	}

	// 计算是否有更多数据（用户端分页使用HasMore格式）
	hasMore := len(list) == req.GetPageSize()

	return &define.GetConversationListResp{
		List:             list,
		HasMore:          hasMore,
		TotalUnreadCount: int(totalUnreadCount),
	}, nil
}

// GetConversationDetail 获取会话详情
func (s *Service) GetConversationDetail(req *define.GetConversationDetailReq) (*define.GetConversationDetailResp, error) {
	userID := s.userService.GetUserId()

	// 参数验证
	if req.ID == "" {
		return nil, define.CC500102Err
	}

	// 验证会话是否属于当前用户
	conversation, err := logic.ValidateConversationAccess(s.ctx, req.ID, userID)
	if err != nil {
		return nil, err
	}

	// 获取对方用户信息
	otherUserInfo, err := facade.GetNewUser(s.ctx, conversation.OtherParticipantID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取对方用户信息失败: %v", err)
		return nil, define.CC500105Err
	}

	// 构造对方用户信息
	otherParticipant := define.UserInfo{
		ID:     conversation.OtherParticipantID,
		Name:   otherUserInfo.Nickname,
		Avatar: otherUserInfo.Avatar,
	}

	return &define.GetConversationDetailResp{
		ID:               conversation.ID,
		OtherParticipant: otherParticipant,
		Status:           conversation.Status,
		CreatedAt:        conversation.CreatedAt,
	}, nil
}
