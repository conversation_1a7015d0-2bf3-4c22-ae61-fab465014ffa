package service

import (
	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	"app_service/pkg/search"
	"app_service/pkg/util/snowflakeutl"
	"errors"
	"strconv"
	"time"

	"gorm.io/gorm"

	log "e.coding.net/g-dtay0385/common/go-logger"
)

// GetApplicationStatus 获取商家申请状态
func (s *Service) GetApplicationStatus(req *define.GetMerchantApplicationStatusReq) (*define.GetMerchantApplicationStatusResp, error) {
	// 获取当前用户ID
	userID := s.userService.GetUserId()

	// 查询用户的申请记录
	merchantAppSchema := repo.GetQuery().MerchantApplication
	qw := search.NewQueryBuilder().
		Eq(merchantAppSchema.UserID, userID).
		OrderByDesc(merchantAppSchema.AppliedAt).
		Build()

	application, err := repo.NewMerchantApplicationRepo(merchantAppSchema.WithContext(s.ctx)).SelectOne(qw)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户没有申请记录，返回空状态
			log.Ctx(s.ctx).Infof("用户[%s]没有商家申请记录", userID)
			return &define.GetMerchantApplicationStatusResp{}, nil
		}
		log.Ctx(s.ctx).Errorf("查询商家申请记录失败，用户ID：%s，错误：%v", userID, err)
		return nil, define.CC500402Err.Err(err)
	}

	// 构建响应数据
	resp := &define.GetMerchantApplicationStatusResp{
		ID:     application.ID,
		UserID: application.UserID,
		Status: application.GetStatus(),
	}

	log.Ctx(s.ctx).Infof("获取商家申请状态成功，用户ID：%s，申请ID：%s，状态：%s", userID, application.ID, application.GetStatus().String())
	return resp, nil
}

// CreateApplication 提交商家申请
func (s *Service) CreateApplication(req *define.SubmitMerchantApplicationReq) (*define.SubmitMerchantApplicationResp, error) {
	// 获取当前用户ID
	userID := s.userService.GetUserId()

	// 检查用户是否已有任何申请记录，不允许重复申请
	merchantAppSchema := repo.GetQuery().MerchantApplication
	existingApp, err := merchantAppSchema.WithContext(s.ctx).
		Where(merchantAppSchema.UserID.Eq(userID)).
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Ctx(s.ctx).Errorf("查询用户现有申请记录失败，用户ID：%s，错误：%v", userID, err)
		return nil, define.CC500402Err.Err(err)
	}

	// 如果存在任何申请记录，不允许重复提交
	if existingApp != nil {
		log.Ctx(s.ctx).Warnf("用户[%s]已有申请记录，不允许重复提交，申请ID：%s", userID, existingApp.ID)
		return nil, define.CC500401Err
	}

	// 生成申请ID
	applicationID := strconv.FormatInt(snowflakeutl.GenerateID(), 10)
	now := time.Now()

	// 创建申请记录
	application := &model.MerchantApplication{
		ID:        applicationID,
		UserID:    userID,
		Status:    int32(enums.ApplicationStatusPending),
		AppliedAt: now,
	}

	// 保存申请记录
	err = repo.NewMerchantApplicationRepo(merchantAppSchema.WithContext(s.ctx)).Save(application)
	if err != nil {
		log.Ctx(s.ctx).Errorf("保存商家申请记录失败，用户ID：%s，错误：%v", userID, err)
		return nil, define.CC500402Err.Err(err)
	}

	log.Ctx(s.ctx).Infof("提交商家申请成功，用户ID：%s，申请ID：%s", userID, applicationID)

	// 返回响应
	resp := &define.SubmitMerchantApplicationResp{
		ID: applicationID,
	}

	return resp, nil
}
